import { Button } from "@/components/ui/button";

export const ConfirmationDialog = ({
  language,
  confirmedTranscript,
  onConfirm,
  onRetry,
}) => (
  <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
    <div className="bg-gray-800 p-6 rounded-lg max-w-md w-full">
      <h3 className="text-lg font-semibold mb-2">
        {language === "hindi" ? "क्या यह सही है?" : "Is this correct?"}
      </h3>
      <p className="mb-4 p-3 bg-gray-700 rounded">{confirmedTranscript}</p>
      <div className="flex gap-3 justify-end">
        <Button variant="outline" onClick={onRetry}>
          {language === "hindi" ? "फिर से बोलें" : "Try Again"}
        </Button>
        <Button
          onClick={onConfirm}
          className="bg-green-600 hover:bg-green-700"
        >
          {language === "hindi" ? "हाँ, सही है" : "Yes, Correct"}
        </Button>
      </div>
    </div>
  </div>
);