import { ChevronDown } from "lucide-react";


export const FAQSection = ({
  language,
  activeFaq,
  setActiveFaq,
}) => (
  <div className="mt-12 border-t border-gray-800 pt-8">
    <h2 className="text-2xl font-bold mb-6">
      {language === "hindi" ? "सामान्य प्रश्न" : "Frequently Asked Questions"}
    </h2>
    
    <div className="space-y-4">
      {[
        {
          id: 1,
          question: language === "hindi" 
            ? "मैं अपना रिज्यूम कैसे संपादित कर सकता हूँ?" 
            : "How can I edit my resume?",
          answer: language === "hindi"
            ? "रिज्यूम को संपादित करने के लिए 'संपादित करें' बटन पर क्लिक करें। आप फिर से सभी जानकारी भर सकते हैं और नया रिज्यूम जनरेट कर सकते हैं।"
            : "Click the 'Edit' button to make changes to your resume. You can fill all the information again and generate a new resume."
        },
        {
          id: 2,
          question: language === "hindi"
            ? "क्या मैं हिंदी और अंग्रेजी दोनों में रिज्यूम बना सकता हूँ?"
            : "Can I create a resume in both Hindi and English?",
          answer: language === "hindi"
            ? "हाँ! आप शुरुआत में भाषा चुन सकते हैं और बाद में दूसरी भाषा में नया रिज्यूम बना सकते हैं।"
            : "Yes! You can select your preferred language at the beginning and create another version in a different language later."
        },
        {
          id: 3,
          question: language === "hindi"
            ? "वॉइस इनपुट सही से काम नहीं कर रहा है, क्या करूँ?"
            : "Voice input isn't working properly, what should I do?",
          answer: language === "hindi"
            ? "1. माइक्रोफोन की अनुमति दें\n2. स्पष्ट और धीरे बोलें\n3. अगर समस्या बनी रहे तो टेक्स्ट इनपुट का उपयोग करें"
            : "1. Allow microphone permissions\n2. Speak clearly and slowly\n3. If issues persist, use text input instead"
        },
        {
          id: 4,
          question: language === "hindi"
            ? "मेरा डेटा सुरक्षित है?"
            : "Is my data secure?",
          answer: language === "hindi"
            ? "हाँ, आपका डेटा केवल आपके डिवाइस पर संग्रहीत किया जाता है और कहीं भी साझा नहीं किया जाता है।"
            : "Yes, your data is stored only on your device and not shared anywhere."
        }
      ].map((faq) => (
        <div key={faq.id} className="bg-gray-800/50 rounded-lg overflow-hidden">
          <button 
            className="w-full flex justify-between items-center p-4 text-left"
            onClick={() => setActiveFaq(activeFaq === faq.id ? null : faq.id)}
          >
            <span className="font-medium">{faq.question}</span>
            <ChevronDown className={`h-5 w-5 transition-transform ${activeFaq === faq.id ? 'rotate-180' : ''}`} />
          </button>
          {activeFaq === faq.id && (
            <div className="px-4 pb-4 text-gray-300 whitespace-pre-line">
              {faq.answer}
            </div>
          )}
        </div>
      ))}
    </div>
  </div>
);