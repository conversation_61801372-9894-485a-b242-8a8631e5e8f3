'use client'
import { motion } from 'framer-motion'
import { Code, Globe, Shield } from 'lucide-react'
import { SparklesIcon } from '@heroicons/react/24/solid'

const CategoryCard = ({ icon: Icon, title, description, href }) => (
  
  <motion.div 
    whileHover={{ y: -5 }}
    className="glass-effect p-6 rounded-xl border border-white/10 hover:border-primary/30 transition-colors"
  >
    <div className="bg-primary/10 p-3 rounded-lg w-14 h-14 flex items-center justify-center mb-4">
      <Icon className="h-8 w-8 text-primary" />
    </div>
    <h3 className="text-xl font-semibold text-white mb-2">{title}</h3>
    <p className="text-gray-400 mb-6">{description}</p>
    <a 
      href={href}
      className="inline-flex items-center text-primary hover:text-primary/80 transition-colors"
    >
      Learn more
      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
      </svg>
    </a>
  </motion.div>
)

const Categories = () => {
  const categories = [
    {
      icon: Code,
      title: 'AI Development',
      description: 'Custom AI solutions that boost efficiency and accelerate business growth.',
      href: '/ai-development',
    },
    {
      icon: Globe,
      title: 'Web Solutions',
      description: 'Cutting-edge web applications powered by AI and modern technologies.',
      href: '/web-solutions',
    },
    {
      icon: Shield,
      title: 'Cyber Security',
      description: 'Advanced security solutions to protect your digital assets and data.',
      href: '/cybersecurity',
    },
  ]

  return (
    <section className="py-20 bg-gradient-to-b from-black to-[#0A0A0A]">
      <div className="container mx-auto px-6">
        <div className="mb-16">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="flex items-center gap-2 mb-4"
          >
            <SparklesIcon className="h-5 w-5 text-primary" />
            <span className="text-sm font-medium text-primary">OUR EXPERTISE</span>
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold text-white max-w-2xl"
          >
            Solving complex challenges with intelligent solutions
          </motion.h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <CategoryCard {...category} />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Categories
