"use client";
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Save,
  Eye,
  EyeOff,
  HelpCircle,
  CheckCircle,
  Home
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useRouter } from 'next/navigation';

const FormHeader = ({
  currentStep,
  totalSteps,
  steps = [],
  onBack,
  onPreview,
  onSave,
  showPreview = false,
  formData = null,
  className = ""
}) => {
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);
  const currentStepData = steps[currentStep];

  // Handle back to home navigation
  const handleBackToHome = () => {
    if (onBack) {
      onBack();
    } else {
      router.push('/');
    }
  };

  // Handle save draft functionality
  const handleSaveDraft = async () => {
    if (!formData) {
      toast.error('No data to save');
      return;
    }

    setIsSaving(true);

    try {
      // Save to localStorage as draft
      const draftData = {
        formData,
        currentStep,
        timestamp: new Date().toISOString(),
        id: Date.now().toString()
      };

      // Get existing drafts
      const existingDrafts = JSON.parse(localStorage.getItem('resumeDrafts') || '[]');

      // Add new draft
      existingDrafts.push(draftData);

      // Keep only last 5 drafts
      if (existingDrafts.length > 5) {
        existingDrafts.splice(0, existingDrafts.length - 5);
      }

      localStorage.setItem('resumeDrafts', JSON.stringify(existingDrafts));

      toast.success('Draft saved successfully!');

      if (onSave) {
        onSave(draftData);
      }
    } catch (error) {
      console.error('Error saving draft:', error);
      toast.error('Failed to save draft');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle help functionality
  const handleHelp = () => {
    toast.success('Help feature coming soon! For now, use the tooltips next to form fields for guidance.');
  };

  return (
    <motion.div
      initial={{ y: -50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className={`sticky top-0 z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-700 ${className}`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-4">
          {/* Left Side - Back Button */}
          <div className="flex items-center space-x-4">
            <motion.button
              onClick={handleBackToHome}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 px-3 py-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-gray-800/50"
              title="Back to Home"
            >
              <Home className="h-4 w-4" />
              <span className="hidden sm:inline font-medium">Back to Home</span>
            </motion.button>
          </div>

          {/* Center - Current Step Info */}
          <div className="flex-1 text-center px-4">
            <h1 className="text-xl font-bold text-white whitespace-nowrap">
              Resume Builder
            </h1>
            <div className="text-sm text-gray-400 mt-1 whitespace-nowrap">
              {currentStepData?.title || 'Loading...'} • Step {currentStep + 1} of {totalSteps}
            </div>
          </div>

          {/* Right Side - Action Buttons */}
          <div className="flex items-center space-x-2">
            {/* Help Button */}
            <motion.button
              onClick={handleHelp}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-gray-400 hover:text-neural-purple transition-colors rounded-lg hover:bg-gray-800/50"
              title="Help & Tips"
            >
              <HelpCircle className="h-5 w-5" />
            </motion.button>

            {/* Preview Button */}
            <motion.button
              onClick={onPreview}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`
                flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 font-medium
                ${showPreview
                  ? 'bg-neural-purple text-white shadow-lg'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                }
              `}
              title={showPreview ? 'Hide Preview' : 'Show Preview'}
            >
              {showPreview ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              <span className="hidden sm:inline">
                {showPreview ? 'Hide' : 'Preview'}
              </span>
            </motion.button>

            {/* Save Draft Button */}
            <motion.button
              onClick={handleSaveDraft}
              disabled={isSaving}
              whileHover={!isSaving ? { scale: 1.05 } : {}}
              whileTap={!isSaving ? { scale: 0.95 } : {}}
              className={`
                flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 font-medium
                ${isSaving
                  ? 'text-gray-500 cursor-not-allowed'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                }
              `}
              title="Save Draft"
            >
              {isSaving ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <Save className="h-4 w-4" />
                </motion.div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span className="hidden sm:inline">
                {isSaving ? 'Saving...' : 'Save'}
              </span>
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default FormHeader;
