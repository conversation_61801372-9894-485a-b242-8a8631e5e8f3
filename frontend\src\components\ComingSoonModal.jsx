'use client';
import { motion } from 'framer-motion';
import { X, Clock, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export const ComingSoonModal = ({ onClose }) => {
  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-br from-gray-900 to-[#0A0A0A] border border-white/10 rounded-xl p-6 max-w-md w-full relative"
      >
        <button 
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
        >
          <X className="h-5 w-5" />
        </button>
        
        <div className="flex flex-col items-center text-center">
          <div className="w-16 h-16 bg-neural-purple/20 rounded-full flex items-center justify-center mb-4">
            <Clock className="h-8 w-8 text-neural-purple" />
          </div>
          
          <h3 className="text-2xl font-bold text-white mb-2">
            Coming Soon!
          </h3>
          
          <p className="text-gray-300 mb-6">
            Our AI Resume Builder is still under development. We're working hard to bring you an amazing experience.
          </p>
          
          <div className="flex items-center gap-2 text-neural-pink mb-6">
            <Sparkles className="h-4 w-4" />
            <span className="text-sm font-medium">Beta Preview</span>
          </div>
          <Link href= './' >
          <Button 
            onClick={onClose}
            className="bg-gradient-to-r from-neural-purple to-neural-pink hover:opacity-90"
          >
            Continue to Home
          </Button>
          </Link>
        </div>
      </motion.div>
    </div>
  );
};