import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Keyboard, Languages, MessageSquareText, Mic } from "lucide-react";


export const WelcomeScreen = ({
  language,
  inputMode,
  onLanguageChange,
  onInputModeChange,
  onCompleteSetup,
}) => (
  <div className="max-w-3xl mx-auto">
    <div className="text-center mb-10">
      <h1 className="text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-500">
        AI Resume Builder
      </h1>
      <p className="text-gray-300 text-lg">
        {language === "hindi"
          ? "अपना रिज्यूम बनाने के लिए अपनी भाषा और इनपुट विधि चुनें"
          : "Choose your language and input method to create your resume"}
      </p>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-10">
      <Card className="bg-gray-900/50 backdrop-blur-md border border-white/10 p-6 rounded-xl">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Languages className="mr-2 h-5 w-5" />
          Select Language
        </h2>
        <div className="grid grid-cols-2 gap-4">
          <Button
            variant={language === "english" ? "default" : "outline"}
            className={`h-20 text-lg ${
              language === "english"
                ? "bg-gradient-to-r from-purple-500 to-pink-500"
                : "bg-gradient-to-r from-purple-500 to-pink-500 opacity-40"
            }`}
            onClick={() => onLanguageChange("english")}
          >
            English
          </Button>
          <Button
            variant={language === "hindi" ? "default" : "outline"}
            className={`h-20 text-lg ${
              language === "hindi"
                ? "bg-gradient-to-r from-purple-500 to-pink-500"
                : "bg-gradient-to-r from-purple-500 to-pink-500 opacity-40"
            }`}
            onClick={() => onLanguageChange("hindi")}
          >
            हिंदी
          </Button>
        </div>
      </Card>

      <Card className="bg-gray-900/50 backdrop-blur-md border border-white/10 p-6 rounded-xl">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <MessageSquareText className="mr-2 h-5 w-5" />
          Select Input Method
        </h2>
        <div className="grid grid-cols-2 gap-4">
          <Button
            variant={inputMode === "text" ? "default" : "outline"}
            className={`h-20 text-lg ${
              inputMode === "text"
                ? "bg-gradient-to-r from-purple-500 to-pink-500"
                : "bg-gradient-to-r from-purple-500 to-pink-500 opacity-40"
            }`}
            onClick={() => onInputModeChange("text")}
          >
            <Keyboard className="mr-2 h-5 w-5" />
            {language === "hindi" ? "टेक्स्ट" : "Text"}
          </Button>
          <Button
            variant={inputMode === "voice" ? "default" : "outline"}
            className={`h-20 text-lg ${
              inputMode === "voice"
                ? "bg-gradient-to-r from-purple-500 to-pink-500 "
                : "bg-gradient-to-r from-purple-500 to-pink-500 opacity-40"
            }`}
            onClick={() => onInputModeChange("voice")}
          >
            <Mic className="mr-2 h-5 w-5" />
            {language === "hindi" ? "आवाज़" : "Voice"}
          </Button>
        </div>
      </Card>
    </div>

    <div className="text-center">
      <Button
        onClick={onCompleteSetup}
        disabled={!language || !inputMode}
        size="lg"
        className="bg-gradient-to-r from-purple-500 to-pink-500 px-8 py-6 text-lg"
      >
        {language === "hindi" ? "शुरू करें" : "Get Started"}
      </Button>
    </div>
  </div>
);