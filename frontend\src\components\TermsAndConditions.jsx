'use client';

import React from 'react';

const TermsAndConditions = () => {
  return (
    <div className="min-h-screen bg-black py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto bg-gray-900 rounded-lg p-8 border border-gray-800">
        <h1 className="text-3xl font-bold text-neural-purple mb-8">Terms and Conditions</h1>
        
        <div className="space-y-6 text-gray-300">
          {[
            {
              title: "1. Agreement to Terms",
              content: "By accessing and using BlinkFind's services, you agree to these terms."
            },
            {
              title: "2. Use License",
              content: "Permission is granted to temporarily access the materials on BlinkFind's website for personal, non-commercial use only. This is the grant of a license, not a transfer of title."
            },
            {
              title: "3. User Responsibilities",
              items: [
                "Provide accurate and complete information",
                "Maintain the security of your account",
                "Comply with all applicable laws and regulations",
                "Not interfere with the proper working of the service"
              ]
            },
            {
              title: "4. Service Modifications",
              content: "BlinkFind reserves the right to modify or discontinue, temporarily or permanently, the service with or without notice. We shall not be liable to you or any third party for any modification, suspension, or discontinuance of the service."
            },
            {
              title: "5. Intellectual Property",
              content: "The service and its original content, features, and functionality are owned by BlinkFind and are protected by international copyright, trademark, patent, trade secret, and other intellectual property laws."
            },
            {
              title: "6. Limitation of Liability",
              content: "In no event shall BlinkFind be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses."
            },
            {
              title: "7. Contact Information",
              content: "We are committed to protecting our users' privacy and maintaining trust.\nIf you have any questions about these Terms and Conditions, please contact <NAME_EMAIL>"
            }
          ].map((section, index) => (
            <section key={index}>
              <h2 className="text-xl font-semibold text-white mb-3">{section.title}</h2>
              {section.content && <p className="mb-4 whitespace-pre-line">{section.content}</p>}
              {section.items && (
                <ul className="list-disc pl-5 space-y-2">
                  {section.items.map((item, i) => (
                    <li key={i}>{item}</li>
                  ))}
                </ul>
              )}
            </section>
          ))}
        </div>

        <div className="mt-8 pt-6 border-t border-gray-700">
          <p className="text-sm text-gray-400">Last updated: {new Date().toLocaleDateString()}</p>
        </div>
      </div>
    </div>
  );
};

export default TermsAndConditions;