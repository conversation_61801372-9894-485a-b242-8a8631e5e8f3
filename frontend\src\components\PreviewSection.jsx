

export const PreviewSection = ({
  formData,
  language,
  isProcessing,
}) => (
  <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10">
    <h2 className="text-xl font-semibold mb-6">
      {language === "hindi" ? "रिज्यूम पूर्वावलोकन" : "Resume Preview"}
      {isProcessing && (
        <span className="ml-2 text-sm text-yellow-400">
          {language === "hindi" ? "प्रोसेसिंग..." : "Processing..."}
        </span>
      )}
    </h2>
    <div className="bg-white text-black p-6 rounded-lg h-full min-h-[500px] overflow-y-auto shadow-lg">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold">
            {formData.personal.name || "Your Name"}
          </h2>
          <div className="flex flex-wrap gap-x-4 gap-y-1 text-sm mt-2">
            {formData.personal.email && (
              <p className="text-gray-700">{formData.personal.email}</p>
            )}
            {formData.personal.phone && (
              <p className="text-gray-700">{formData.personal.phone}</p>
            )}
          </div>
        </div>

        {(formData.education.degree ||
          formData.education.institution ||
          formData.education.field ||
          formData.education.graduationYear) && (
          <div>
            <h3 className="text-lg font-semibold border-b pb-1">Education</h3>
            <div className="mt-2">
              {formData.education.degree && (
                <p className="font-medium">{formData.education.degree}</p>
              )}
              {formData.education.institution && (
                <p className="text-sm">{formData.education.institution}</p>
              )}
              {formData.education.field && (
                <p className="text-sm">{formData.education.field}</p>
              )}
              {formData.education.graduationYear && (
                <p className="text-sm">
                  Graduated: {formData.education.graduationYear}
                </p>
              )}
            </div>
          </div>
        )}

        <div className="opacity-50">
          <h3 className="text-lg font-semibold border-b pb-1">
            Work Experience
          </h3>
          <p className="text-sm mt-2">
            [Your work experience will appear here]
          </p>
        </div>

        <div className="opacity-50">
          <h3 className="text-lg font-semibold border-b pb-1">Skills</h3>
          <p className="text-sm mt-2">[Your skills will appear here]</p>
        </div>
      </div>
    </div>
  </div>
);