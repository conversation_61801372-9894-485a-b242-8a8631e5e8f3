"use client";
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  User, 
  GraduationCap, 
  Briefcase, 
  Award,
  FileText, 
  ArrowLeft,
  ArrowRight,
  Sparkles,
  Eye,
  Wand2,
  Plus,
  Trash2,
  MapPin,
  Calendar,
  Globe,
  Mail,
  Phone,
  Link,
  Upload,
  PlusCircle,
  Palette
} from "lucide-react";
import { SparklesIcon } from '@heroicons/react/24/solid';
import { toast } from "react-hot-toast";
import ProgressBar from "@/components/ProgressBar";
import ATSScoreCircle from "@/components/ATSScoreCircle";
import EnhancedResumePreview from "@/components/EnhancedResumePreview";
import PDFDownload, { ViewResumeButton } from "@/components/PDFDownload";
import ResumeBuilderModeToggle from "@/components/ResumeBuilderModeToggle";
import ResumeUpload from "@/components/ResumeUpload";
import ATSAnalysisDisplay from "@/components/ATSAnalysisDisplay";
import JobDescriptionInput from "@/components/JobDescriptionInput";
import BeforeAfterComparison from "@/components/BeforeAfterComparison";
import AIContentSuggestions from "@/components/AIContentSuggestions";
import ATSOptimizationPanel from "@/components/ATSOptimizationPanel";
import TemplateSelector from "@/components/TemplateSelector";
import UploadEnhancementWorkflow from "@/components/UploadEnhancementWorkflow";
import useATSAnalysis from "@/hooks/useATSAnalysis";
import ATSFieldIndicator from "@/components/ATSFieldIndicator";
import ClientOnly from "@/components/ClientOnly";
import ATSTooltip from "@/components/ATSTooltip";
import StepIndicator from "@/components/StepIndicator";
import StickyNavigation from "@/components/StickyNavigation";
import FormHeader from "@/components/FormHeader";
import { ExperienceForm, SkillsProjectsForm, ReviewForm, PersonalInfoForm, EducationForm } from "@/components/ResumeFormComponents";

const ResumeBuilder = () => {
  // Main state management
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showProgressBar, setShowProgressBar] = useState(false);
  const [resumeGenerated, setResumeGenerated] = useState(false);
  const [resumeUrl, setResumeUrl] = useState("");
  const [resumeData, setResumeData] = useState(null);
  const [atsScore, setAtsScore] = useState(null);
  const [suggestions, setSuggestions] = useState([]);
  const [showPreview, setShowPreview] = useState(false);
  const [generationError, setGenerationError] = useState(null); // State for generation errors

  // New state for upload functionality
  const [builderMode, setBuilderMode] = useState('create'); // 'create', 'upload', 'analyze'
  const [uploadAnalysis, setUploadAnalysis] = useState(null);
  const [showAnalysis, setShowAnalysis] = useState(false);
  const [showUploadWorkflow, setShowUploadWorkflow] = useState(false);

  // Enhanced upload workflow state
  const [showJobDescriptionInput, setShowJobDescriptionInput] = useState(false);
  const [isGeneratingTargeted, setIsGeneratingTargeted] = useState(false);
  const [targetedResumeData, setTargetedResumeData] = useState(null);

  // Template selection state
  const [selectedTemplate, setSelectedTemplate] = useState('modern');
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);

  // Form validation state
  const [validationErrors, setValidationErrors] = useState({});
  const [showValidationErrors, setShowValidationErrors] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    personal: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      location: "",
      linkedin: "",
      portfolio: "",
      summary: ""
    },
    education: [
      {
        id: 1,
        degree: "",
        institution: "",
        location: "",
        startDate: "",
        endDate: "",
        gpa: "",
        relevant: ""
      }
    ],
    experience: [
      {
        id: 1,
        title: "",
        company: "",
        location: "",
        startDate: "",
        endDate: "",
        current: false,
        description: ""
      }
    ],
    skills: {
      technical: [],
      languages: [],
      certifications: []
    },
    projects: [
      {
        id: 1,
        name: "",
        description: "",
        technologies: "",
        link: ""
      }
    ],
    jobDescription: ""
  });

  // Real-time ATS analysis
  const atsAnalysis = useATSAnalysis(formData);

  // Form validation functions
  const validateStep = (stepIndex) => {
    const errors = {};
    
    switch (stepIndex) {
      case 0: // Personal Information
        if (!formData.personal.firstName.trim()) {
          errors.firstName = 'First name is required';
        }
        if (!formData.personal.lastName.trim()) {
          errors.lastName = 'Last name is required';
        }
        if (!formData.personal.email.trim()) {
          errors.email = 'Email is required';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.personal.email)) {
          errors.email = 'Please enter a valid email address';
        }
        break;
        
      case 1: // Education
        const validEducation = formData.education.filter(edu => 
          edu.degree.trim() && edu.institution.trim()
        );
        if (validEducation.length === 0) {
          errors.education = 'At least one education entry with degree and institution is required';
        }
        break;
        
      case 2: // Experience
        const validExperience = formData.experience.filter(exp => 
          exp.title.trim() && exp.company.trim()
        );
        if (validExperience.length === 0) {
          errors.experience = 'At least one work experience entry with job title and company is required';
        }
        break;
        
      case 3: // Skills & Projects
        if (formData.skills.technical.length === 0 && 
            formData.skills.languages.length === 0 && 
            formData.skills.certifications.length === 0) {
          errors.skills = 'At least one skill (technical, languages, or certifications) is required';
        }
        break;
        
      default:
        break;
    }
    
    return errors;
  };

  const canProceedToNextStep = (stepIndex) => {
    const errors = validateStep(stepIndex);
    return Object.keys(errors).length === 0;
  };

  const canGenerateResume = () => {
    // Check all steps
    for (let i = 0; i < steps.length - 1; i++) {
      if (!canProceedToNextStep(i)) {
        return false;
      }
    }
    return true;
  };

  // Steps configuration
  const steps = [
    {
      id: 0,
      title: "Personal Information",
      icon: User,
      description: "Tell us about yourself"
    },
    {
      id: 1,
      title: "Education",
      icon: GraduationCap,
      description: "Your academic background"
    },
    {
      id: 2,
      title: "Experience",
      icon: Briefcase,
      description: "Your work experience"
    },
    {
      id: 3,
      title: "Skills & Projects",
      icon: Award,
      description: "Showcase your abilities"
    },
    {
      id: 4,
      title: "Review & Generate",
      icon: FileText,
      description: "Finalize your resume"
    }
  ];

  // Update form data
  const updateFormData = (section, field, value, index = null) => {
    setFormData(prev => {
      if (index !== null && Array.isArray(prev[section])) {
        const newArray = [...prev[section]];
        newArray[index] = { ...newArray[index], [field]: value };
        return { ...prev, [section]: newArray };
      } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {
        return {
          ...prev,
          [section]: { ...prev[section], [field]: value }
        };
      }
      return prev;
    });
  };

  // Add new item to array sections
  const addArrayItem = (section, template) => {
    setFormData(prev => ({
      ...prev,
      [section]: [...prev[section], { ...template, id: Math.random().toString(36).substring(2, 11) }]
    }));
  };

  // Remove item from array sections
  const removeArrayItem = (section, id) => {
    setFormData(prev => ({
      ...prev,
      [section]: prev[section].filter(item => item.id !== id)
    }));
  };

  // Navigation functions
  const nextStep = () => {
    // Validate current step before proceeding
    const errors = validateStep(currentStep);
    
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      setShowValidationErrors(true);
      
      // Show error toast
      const errorMessages = Object.values(errors);
      toast.error(errorMessages[0]); // Show first error
      return;
    }
    
    // Clear validation errors if step is valid
    setValidationErrors({});
    setShowValidationErrors(false);
    
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    // Clear validation errors when going back
    setValidationErrors({});
    setShowValidationErrors(false);
    
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Generate resume with AI
  const generateResume = async () => {
    // Clear previous validation errors and generation error at the start
    setValidationErrors({});
    setShowValidationErrors(false);
    setGenerationError(null);

    // Validate all steps before generating
    if (!canGenerateResume()) {
      const allErrors = {};
      for (let i = 0; i < steps.length - 1; i++) {
        const stepErrors = validateStep(i);
        Object.assign(allErrors, stepErrors);
      }

      setValidationErrors(allErrors);
      setShowValidationErrors(true);

      // Find first step with errors and navigate to it
      for (let i = 0; i < steps.length - 1; i++) {
        const stepErrors = validateStep(i);
        if (Object.keys(stepErrors).length > 0) {
          setCurrentStep(i);
          toast.error(`Please complete all required fields in ${steps[i].title}`);
          return;
        }
      }
      return;
    }

    try {
      console.log('🚀 Starting resume generation...');
      console.log('📝 Form data sent to API:', JSON.stringify(formData, null, 2)); // Log full form data

      setIsGenerating(true);
      setShowProgressBar(true);
      setResumeGenerated(false); // Reset generated state at the start
      setResumeData(null); // Clear previous resume data
      setResumeUrl(''); // Clear previous resume URL
      setAtsScore(null); // Clear previous ATS score
      setSuggestions([]); // Clear previous suggestions

      const response = await fetch('/api/generate-resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formData,
          templateId: selectedTemplate
        }),
      });

      console.log('📡 API response status:', response.status);
      console.log('📡 API response headers:', Object.fromEntries(response.headers.entries()));

      // Check if response is actually JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const textResponse = await response.text();
        console.error('❌ Non-JSON response received:', textResponse);
        setGenerationError('Server returned an unexpected response format.');
        throw new Error('Server returned non-JSON response'); // Throw to enter catch block
      }

      const data = await response.json();
      console.log('📊 API response data received:', JSON.stringify(data, null, 2)); // Log full response data

      if (!response.ok) {
        // Handle API errors, including validation errors from backend
        const errorMessage = data.error || data.message || 'Failed to generate resume';
        console.error('💥 API Error:', errorMessage);
        // If backend returns validation errors, update frontend state
        if (data.validationErrors) {
           console.log('Backend Validation Errors:', JSON.stringify(data.validationErrors, null, 2));
           setValidationErrors(data.validationErrors);
           setShowValidationErrors(true);
           // Optionally navigate to the first step with errors
           const firstErrorStep = steps.findIndex(step => 
             Object.keys(data.validationErrors).some(field => field.startsWith(step.id === 0 ? 'personal' : step.id === 1 ? 'education' : step.id === 2 ? 'experience' : step.id === 3 ? 'skills' : ''))
           );
           if(firstErrorStep !== -1) {
             setCurrentStep(firstErrorStep);
           } else {
             // If validation errors don't map to a step, just show them on the current step or review step
             setCurrentStep(steps.length - 1); // Move to review to show errors
           }
           setGenerationError('Please fix the validation errors.'); // Set a generic error message for the user
        } else {
           setGenerationError(errorMessage); // Set the error message from the API
        }
        throw new Error(errorMessage); // Throw to enter catch block
      }

      // Check if essential data is present in the successful response
      if (!data.resumeData || !data.downloadUrl) {
          console.error('❌ API response missing essential data:', data);
          // Set an error message if data is incomplete but response was technically 'ok'
          setGenerationError('Generated resume data is incomplete. Please try again or contact support.');
          throw new Error('Generated resume data is incomplete.'); // Still throw to enter catch block for consistent state reset
      }

      // Store the enhanced resume data and ATS information
      console.log('✅ Setting resume data...');
      setResumeUrl(data.downloadUrl);
      setResumeData(data.resumeData);
      setAtsScore(data.atsScore || data.resumeData?.atsScore?.overall || 75);
      setSuggestions(data.suggestions || data.resumeData?.atsScore?.improvements || []);

      console.log('🎉 Resume generation completed successfully');
      // Set resumeGenerated to true ONLY if essential data is present
      setResumeGenerated(true);

    } catch (error) {
      console.error("💥 Error during resume generation process:", error);
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      // Set generation error state if not already set by incomplete data check or backend validation
      if (!generationError) {
         setGenerationError(error.message || "Failed to generate resume");
         toast.error(error.message || "Failed to generate resume");
      }

    } finally {
      setIsGenerating(false);
      setShowProgressBar(false);
    }
  };

  const handleProgressComplete = () => {
    setShowProgressBar(false);
    setIsGenerating(false);

    // Show success message ONLY if resume was successfully generated AND there's no generation error
    if (resumeGenerated && !generationError) {
      const scoreMessage = atsScore
        ? `Resume generated! ATS Score: ${atsScore}%`
        : "Your resume has been generated successfully!";
      toast.success(scoreMessage);
    }
    // If not resumeGenerated or there's a generationError, an appropriate toast/error message would have been shown earlier
  };

  // Handle upload analysis completion
  const handleUploadAnalysis = (analysisData) => {
    console.log('📊 Upload analysis received:', analysisData);
    setUploadAnalysis(analysisData);

    // For upload & enhance mode, show job description input
    if (builderMode === 'upload') {
      setShowJobDescriptionInput(true);
    } else {
      // For quick analysis, show results immediately
      setShowAnalysis(true);
    }

    // If it's a full analysis, populate form data (even with minimal data)
    if (analysisData.analysisType === 'full' && analysisData.extractedData) {
      const extracted = analysisData.extractedData;
      console.log('📋 Extracted data for form population:', extracted);

      // Update form data with extracted information
      setFormData(prevData => ({
        ...prevData,
        personal: {
          ...prevData.personal,
          firstName: extracted.personal?.firstName || prevData.personal.firstName,
          lastName: extracted.personal?.lastName || prevData.personal.lastName,
          email: extracted.personal?.email || prevData.personal.email,
          phone: extracted.personal?.phone || prevData.personal.phone,
          location: extracted.personal?.location || prevData.personal.location,
          linkedin: extracted.personal?.linkedin || prevData.personal.linkedin,
          portfolio: extracted.personal?.portfolio || prevData.personal.portfolio,
          summary: extracted.personal?.summary || prevData.personal.summary
        },
        education: extracted.education?.length > 0 ? extracted.education.map(edu => ({
          ...edu,
          id: edu.id || Date.now() + Math.random()
        })) : prevData.education,
        experience: extracted.experience?.length > 0 ? extracted.experience.map(exp => ({
          ...exp,
          id: exp.id || Date.now() + Math.random()
        })) : prevData.experience,
        skills: {
          technical: extracted.skills?.technical?.length > 0 ? extracted.skills.technical : prevData.skills.technical,
          languages: extracted.skills?.languages?.length > 0 ? extracted.skills.languages : prevData.skills.languages,
          certifications: extracted.skills?.certifications?.length > 0 ? extracted.skills.certifications : prevData.skills.certifications
        },
        projects: extracted.projects?.length > 0 ? extracted.projects.map(proj => ({
          ...proj,
          id: proj.id || Date.now() + Math.random()
        })) : prevData.projects
      }));

      // Set ATS score and suggestions
      if (analysisData.atsScore) {
        setAtsScore(analysisData.atsScore.overall);
        setSuggestions(analysisData.enhancements?.suggestions || analysisData.analysis?.recommendations || []);
      }

      console.log('✅ Form data updated with extracted information');
    } else if (analysisData.fallback) {
      console.log('⚠️ Using fallback data - minimal extraction');
      // Even with fallback, try to extract any available information
      if (analysisData.extractedData) {
        const extracted = analysisData.extractedData;
        setFormData(prevData => ({
          ...prevData,
          personal: {
            ...prevData.personal,
            summary: extracted.personal?.summary || 'Please add your professional summary here.'
          }
        }));
      }
    }
  };

  // Handle job description submission for targeted resume generation
  const handleJobDescriptionSubmit = async (jobData) => {
    if (!uploadAnalysis?.extractedData) {
      toast.error('No resume data available. Please upload a resume first.');
      return;
    }

    try {
      setIsGeneratingTargeted(true);
      console.log('🎯 Generating targeted resume with job data:', jobData);

      const response = await fetch('/api/generate-targeted-resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          extractedResumeData: uploadAnalysis.extractedData,
          jobDescription: jobData.description,
          jobTitle: jobData.jobTitle,
          company: jobData.company
        }),
      });

      const data = await response.json();
      console.log('📊 Targeted resume response:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate targeted resume');
      }

      // Update form data with enhanced resume
      if (data.enhancedResume) {
        // Properly structure the enhanced resume data to match form data structure
        const enhancedFormData = {
          personal: {
            firstName: data.enhancedResume.personal?.firstName || '',
            lastName: data.enhancedResume.personal?.lastName || '',
            email: data.enhancedResume.personal?.email || '',
            phone: data.enhancedResume.personal?.phone || '',
            location: data.enhancedResume.personal?.location || '',
            linkedin: data.enhancedResume.personal?.linkedin || '',
            portfolio: data.enhancedResume.personal?.portfolio || '',
            summary: data.enhancedResume.personal?.summary || ''
          },
          education: data.enhancedResume.education?.length > 0
            ? data.enhancedResume.education.map(edu => ({
                id: edu.id || Date.now() + Math.random(),
                degree: edu.degree || '',
                institution: edu.institution || '',
                location: edu.location || '',
                startDate: edu.startDate || '',
                endDate: edu.endDate || '',
                gpa: edu.gpa || '',
                relevant: edu.relevant || ''
              }))
            : [{ id: 1, degree: "", institution: "", location: "", startDate: "", endDate: "", gpa: "", relevant: "" }],
          experience: data.enhancedResume.experience?.length > 0
            ? data.enhancedResume.experience.map(exp => ({
                id: exp.id || Date.now() + Math.random(),
                title: exp.title || '',
                company: exp.company || '',
                location: exp.location || '',
                startDate: exp.startDate || '',
                endDate: exp.endDate || '',
                current: exp.current || false,
                description: exp.description || (exp.achievements ? exp.achievements.join('\n') : '')
              }))
            : [{ id: 1, title: "", company: "", location: "", startDate: "", endDate: "", current: false, description: "" }],
          skills: {
            technical: data.enhancedResume.skills?.technical || [],
            languages: data.enhancedResume.skills?.languages || [],
            certifications: data.enhancedResume.skills?.certifications || []
          },
          projects: data.enhancedResume.projects?.length > 0
            ? data.enhancedResume.projects.map(proj => ({
                id: proj.id || Date.now() + Math.random(),
                name: proj.name || '',
                description: proj.description || '',
                technologies: proj.technologies || '',
                link: proj.link || ''
              }))
            : [{ id: 1, name: "", description: "", technologies: "", link: "" }]
        };

        setFormData(enhancedFormData);
        setTargetedResumeData(data);
        setAtsScore(data.atsScore?.overall || 85);
        setSuggestions(data.recommendations || []);

        // Show success and move to form editing
        setShowJobDescriptionInput(false);
        setCurrentStep(0);
        toast.success('Resume optimized for the target job!');
      }

    } catch (error) {
      console.error('Targeted resume generation error:', error);
      toast.error(error.message || 'Failed to generate targeted resume');
    } finally {
      setIsGeneratingTargeted(false);
    }
  };

  // Handle mode change
  const handleModeChange = (mode) => {
    setBuilderMode(mode);
    setUploadAnalysis(null);
    setShowAnalysis(false);
    setResumeGenerated(false);
    setCurrentStep(0);
    setShowJobDescriptionInput(false);
    setIsGeneratingTargeted(false);
    setTargetedResumeData(null);

    // Show enhanced workflow for upload mode
    if (mode === 'upload') {
      setShowUploadWorkflow(true);
    } else {
      setShowUploadWorkflow(false);
    }
  };

  // Handle upload workflow completion
  const handleUploadWorkflowComplete = (enhancedData) => {
    console.log('🎉 Upload workflow completed with data:', enhancedData);

    // Set the enhanced data
    if (enhancedData.enhancedResume) {
      // Use enhanced resume data
      const enhanced = enhancedData.enhancedResume;
      setFormData({
        personal: {
          firstName: enhanced.personal?.firstName || '',
          lastName: enhanced.personal?.lastName || '',
          email: enhanced.personal?.email || '',
          phone: enhanced.personal?.phone || '',
          location: enhanced.personal?.location || '',
          linkedin: enhanced.personal?.linkedin || '',
          portfolio: enhanced.personal?.portfolio || '',
          summary: enhanced.personal?.summary || ''
        },
        education: enhanced.education?.length > 0 ? enhanced.education.map(edu => ({
          id: edu.id || Date.now() + Math.random(),
          degree: edu.degree || '',
          institution: edu.institution || '',
          location: edu.location || '',
          startDate: edu.startDate || '',
          endDate: edu.endDate || '',
          gpa: edu.gpa || '',
          relevant: edu.relevant || ''
        })) : [{ id: 1, degree: "", institution: "", location: "", startDate: "", endDate: "", gpa: "", relevant: "" }],
        experience: enhanced.experience?.length > 0 ? enhanced.experience.map(exp => ({
          id: exp.id || Date.now() + Math.random(),
          title: exp.title || '',
          company: exp.company || '',
          location: exp.location || '',
          startDate: exp.startDate || '',
          endDate: exp.endDate || '',
          current: exp.current || false,
          description: exp.description || ''
        })) : [{ id: 1, title: "", company: "", location: "", startDate: "", endDate: "", current: false, description: "" }],
        skills: {
          technical: enhanced.skills?.technical || [],
          languages: enhanced.skills?.languages || [],
          certifications: enhanced.skills?.certifications || []
        },
        projects: enhanced.projects?.length > 0 ? enhanced.projects.map(proj => ({
          id: proj.id || Date.now() + Math.random(),
          name: proj.name || '',
          description: proj.description || '',
          technologies: proj.technologies || '',
          link: proj.link || ''
        })) : [{ id: 1, name: "", description: "", technologies: "", link: "" }]
      });

      setAtsScore(enhancedData.atsScore?.overall || 85);
      setSuggestions(enhancedData.recommendations || []);
    } else if (enhancedData.extractedData) {
      // Use extracted data from upload
      const extracted = enhancedData.extractedData;
      setFormData({
        personal: {
          firstName: extracted.personal?.firstName || '',
          lastName: extracted.personal?.lastName || '',
          email: extracted.personal?.email || '',
          phone: extracted.personal?.phone || '',
          location: extracted.personal?.location || '',
          linkedin: extracted.personal?.linkedin || '',
          portfolio: extracted.personal?.portfolio || '',
          summary: extracted.personal?.summary || ''
        },
        education: extracted.education?.length > 0 ? extracted.education.map(edu => ({
          id: edu.id || Date.now() + Math.random(),
          degree: edu.degree || '',
          institution: edu.institution || '',
          location: edu.location || '',
          startDate: edu.startDate || '',
          endDate: edu.endDate || '',
          gpa: edu.gpa || '',
          relevant: edu.relevant || ''
        })) : [{ id: 1, degree: "", institution: "", location: "", startDate: "", endDate: "", gpa: "", relevant: "" }],
        experience: extracted.experience?.length > 0 ? extracted.experience.map(exp => ({
          id: exp.id || Date.now() + Math.random(),
          title: exp.title || '',
          company: exp.company || '',
          location: exp.location || '',
          startDate: exp.startDate || '',
          endDate: exp.endDate || '',
          current: exp.current || false,
          description: exp.description || ''
        })) : [{ id: 1, title: "", company: "", location: "", startDate: "", endDate: "", current: false, description: "" }],
        skills: {
          technical: extracted.skills?.technical || [],
          languages: extracted.skills?.languages || [],
          certifications: extracted.skills?.certifications || []
        },
        projects: extracted.projects?.length > 0 ? extracted.projects.map(proj => ({
          id: proj.id || Date.now() + Math.random(),
          name: proj.name || '',
          description: proj.description || '',
          technologies: proj.technologies || '',
          link: proj.link || ''
        })) : [{ id: 1, name: "", description: "", technologies: "", link: "" }]
      });

      setAtsScore(enhancedData.atsScore?.overall || 75);
      setSuggestions(enhancedData.enhancements?.suggestions || []);
    }

    // Hide workflow and show form
    setShowUploadWorkflow(false);
    setCurrentStep(0);
    toast.success('Resume data loaded! You can now edit and enhance it further.');
  };

  // Reset to create mode
  const resetToCreateMode = () => {
    setBuilderMode('create');
    setUploadAnalysis(null);
    setShowAnalysis(false);
    setResumeGenerated(false);
    setCurrentStep(0);
    setShowUploadWorkflow(false);
    setFormData({
      personal: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        location: "",
        linkedin: "",
        portfolio: "",
        summary: ""
      },
      education: [{ id: 1, degree: "", institution: "", location: "", startDate: "", endDate: "", gpa: "", relevant: "" }],
      experience: [{ id: 1, title: "", company: "", location: "", startDate: "", endDate: "", current: false, description: "" }],
      skills: { technical: [], languages: [], certifications: [] },
      projects: [{ id: 1, name: "", description: "", technologies: "", link: "" }]
    });
  };

  // Floating background elements with deterministic values to avoid hydration mismatch
  const FloatingElements = () => {
    // Use deterministic values to avoid hydration mismatch
    const elements = [
      { width: 180, height: 160, left: 10, top: 20, duration: 15, x: 30, y: 40 },
      { width: 220, height: 190, left: 80, top: 60, duration: 18, x: -25, y: -30 },
      { width: 150, height: 140, left: 60, top: 80, duration: 12, x: 35, y: 25 },
      { width: 200, height: 170, left: 30, top: 40, duration: 20, x: -40, y: 35 },
      { width: 170, height: 200, left: 90, top: 10, duration: 16, x: 20, y: -45 },
      { width: 190, height: 150, left: 20, top: 70, duration: 14, x: -30, y: 20 },
      { width: 160, height: 180, left: 70, top: 30, duration: 22, x: 45, y: -25 },
      { width: 210, height: 160, left: 50, top: 90, duration: 17, x: -20, y: 30 }
    ];

    return (
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {elements.map((element, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full opacity-10 blur-xl"
            style={{
              backgroundColor: '#832ED3',
              width: element.width,
              height: element.height,
              left: element.left + '%',
              top: element.top + '%',
            }}
            animate={{
              x: [0, element.x, 0],
              y: [0, element.y, 0],
            }}
            transition={{
              duration: element.duration,
              repeat: Infinity,
              repeatType: 'reverse',
              ease: "easeInOut"
            }}
          />
        ))}
      </div>
    );
  };

  // Show enhanced upload workflow
  if (showUploadWorkflow) {
    return (
      <UploadEnhancementWorkflow
        onComplete={handleUploadWorkflowComplete}
        onBack={() => {
          setShowUploadWorkflow(false);
          setBuilderMode('create');
        }}
      />
    );
  }

  // Show upload analysis results for quick ATS check
  if (builderMode === 'analyze' && showAnalysis && uploadAnalysis) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
        </div>

        <FloatingElements />

        <div className="relative z-10 container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-center mb-8">
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
                ATS Analysis Results
              </h1>
              <p className="text-gray-300">
                Here's your comprehensive resume analysis and recommendations
              </p>
            </div>

            <ATSAnalysisDisplay
              analysisData={uploadAnalysis}
              analysisType="quick"
            />

            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
              <motion.button
                onClick={() => handleModeChange('upload')}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Upload className="h-5 w-5" />
                Enhance This Resume
              </motion.button>

              <motion.button
                onClick={resetToCreateMode}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <PlusCircle className="h-5 w-5" />
                Start Fresh
              </motion.button>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  // Show success screen if resume is generated and data is available
  if (resumeGenerated && resumeData && resumeUrl) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative">
        {/* Grid background */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
        </div>

        <FloatingElements />

        <div className="relative z-10 container mx-auto px-4 py-16">
          <motion.div
            className="max-w-4xl mx-auto text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Success Header */}
            <div className="mb-8">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="w-20 h-20 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6"
              >
                <Sparkles className="h-10 w-10 text-white" />
              </motion.div>

              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                Resume Generated Successfully!
              </h1>
              <p className="text-xl text-gray-300">
                Your AI-optimized resume is ready for download
              </p>
            </div>

            {/* ATS Score Display */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* ATS Score Circle */}
              <motion.div
                className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <h3 className="text-xl font-semibold mb-6 text-center">ATS Compatibility Score</h3>
                <div className="flex justify-center">
                  <ATSScoreCircle score={atsScore || 75} size={150} />
                </div>
              </motion.div>

              {/* Suggestions */}
              <motion.div
                className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-8 border border-white/10"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <h3 className="text-xl font-semibold mb-6 text-center">AI Suggestions & Improvements</h3>
                <div className="space-y-4 max-h-60 overflow-y-auto pr-2">
                  {suggestions && suggestions.length > 0 ? (
                    suggestions.map((suggestion, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                        className="bg-gray-800/50 p-3 rounded-lg border border-gray-700"
                      >
                        <p className="text-sm text-gray-300">
                          {typeof suggestion === 'string' ? suggestion : suggestion.message || suggestion}
                        </p>
                      </motion.div>
                    ))
                  ) : (
                    <p className="text-gray-400 text-center">No specific recommendations at this time.</p>
                  )}
                </div>
              </motion.div>
            </div>

            {/* Action Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1 }}
            >
              {/* PDF Download Button */}
              {resumeUrl && (
                <PDFDownload
                  formData={formData}
                  resumeData={resumeData}
                  templateId={selectedTemplate}
                  downloadUrl={resumeUrl}
                />
              )}

              {/* View Resume Button */}
              {resumeData && (
                <ViewResumeButton
                  formData={formData}
                  resumeData={resumeData}
                  templateId={selectedTemplate}
                />
              )}

              {/* Create Another Resume Button */}
              <motion.button
                onClick={resetToCreateMode}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Wand2 className="h-5 w-5" />
                Create Another Resume
              </motion.button>
            </motion.div>

            {/* Resume Preview (Optional on success screen) */}
            {showPreview && resumeData && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mb-8"
              >
                <div className="max-w-4xl mx-auto">
                  <EnhancedResumePreview formData={formData} />
                </div>
              </motion.div>
            )}

            {/* Template Selector */}
            <motion.div
              className="mt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
            >
              <h3 className="text-xl font-semibold mb-4 text-center">Choose a Template</h3>
              <TemplateSelector
                selectedTemplate={selectedTemplate}
                onTemplateSelect={setSelectedTemplate}
                // onClose is not needed here as it's part of the success screen
              />
            </motion.div>

          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative pt-16">
      {/* Form Header */}
      <FormHeader
        currentStep={currentStep}
        totalSteps={steps.length}
        steps={steps}
        onBack={() => {
          // Navigate back to home page
          window.location.href = '/';
        }}
        onPreview={() => setShowPreview(!showPreview)}
        onSave={(draftData) => {
          console.log('Draft saved:', draftData);
          // Could implement additional save logic here
        }}
        showPreview={showPreview}
        formData={formData}
      />

      {/* Grid background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>

      <FloatingElements />

      <ProgressBar
        isVisible={showProgressBar}
        onComplete={handleProgressComplete}
      />

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          className="text-center mb-8 md:mb-12 relative z-20"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center gap-3 mb-4 md:mb-6">
            <div className="relative">
              <SparklesIcon className="h-8 w-8 md:h-10 md:w-10 text-neural-pink animate-pulse" />
              <div className="absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md"></div>
            </div>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white">
              AI Resume Builder
            </h1>
          </div>
          <p className="text-gray-300 text-base md:text-lg max-w-2xl mx-auto px-4">
            Create professional, ATS-friendly resumes in minutes with our AI-powered builder
          </p>

          {/* Progress indicator - only show for create mode */}
          {builderMode === 'create' && (
            <div className="mt-6 flex items-center justify-center">
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-full px-4 py-2 border border-gray-700">
                <span className="text-sm text-gray-400">Step {currentStep + 1} of {steps.length}</span>
              </div>
            </div>
          )}
        </motion.div>

        {/* Mode Toggle - only show if not in create mode or at step 0 */}
        {(builderMode !== 'create' || currentStep === 0) && !resumeGenerated && (
          <ResumeBuilderModeToggle
            currentMode={builderMode}
            onModeChange={handleModeChange}
          />
        )}

        {/* Upload Section */}
        {(builderMode === 'upload' || builderMode === 'analyze') && !showAnalysis && !showJobDescriptionInput && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-2xl mx-auto mb-8"
          >
            <ResumeUpload
              onAnalysisComplete={handleUploadAnalysis}
              analysisType={builderMode === 'analyze' ? 'quick' : 'full'}
            />
          </motion.div>
        )}

        {/* Job Description Input for Upload & Enhance */}
        {builderMode === 'upload' && showJobDescriptionInput && uploadAnalysis && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-2xl mx-auto mb-8"
          >
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Resume Uploaded Successfully!</h2>
              <p className="text-gray-300">Now provide the job description to create a targeted, ATS-optimized resume</p>
            </div>

            <JobDescriptionInput
              onJobDescriptionSubmit={handleJobDescriptionSubmit}
              isLoading={isGeneratingTargeted}
            />

            <div className="flex justify-center mt-6">
              <motion.button
                onClick={() => {
                  setShowJobDescriptionInput(false);
                  setCurrentStep(0);
                }}
                className="inline-flex items-center gap-2 px-6 py-3 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Skip Job Targeting
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* Show analysis results for upload mode */}
        {builderMode === 'analyze' && showAnalysis && uploadAnalysis && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-4xl mx-auto mb-8"
          >
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Resume Analysis Complete</h2>
              <p className="text-gray-300">Review the extracted information and continue to enhance your resume</p>
            </div>

            <ATSAnalysisDisplay
              analysisData={uploadAnalysis}
              analysisType="quick"
            />

            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
              <motion.button
                onClick={() => handleModeChange('upload')}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-bold rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Upload className="h-5 w-5" />
                Enhance This Resume
              </motion.button>

              <motion.button
                onClick={resetToCreateMode}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gray-800/80 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <PlusCircle className="h-5 w-5" />
                Start Fresh
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* Display Generation Error */}
        {!isGenerating && !resumeGenerated && generationError && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-md mx-auto text-center bg-red-500/10 border border-red-500/20 rounded-xl p-4 mb-8"
          >
            <h3 className="text-lg font-semibold text-red-400 mb-2">Generation Failed</h3>
            <p className="text-red-300 text-sm">{generationError}</p>
            <p className="text-gray-400 text-xs mt-2">Please check the form for missing information and try again.</p>
          </motion.div>
        )}

        {/* Regular form flow - only show for create mode or after upload analysis */}
        {(builderMode === 'create' || (builderMode === 'upload' && currentStep >= 0 && !showJobDescriptionInput)) && !resumeGenerated && (
          <>
            {/* Enhanced Step Indicator */}
            <motion.div
              className="mb-8 md:mb-12"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <StepIndicator
                currentStep={currentStep}
                totalSteps={steps.length}
                steps={steps}
                onStepClick={(stepIndex) => {
                  if (stepIndex <= currentStep) {
                    setCurrentStep(stepIndex);
                  }
                }}
                allowClickNavigation={true}
              />
            </motion.div>



        {/* Main Content Area */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8 max-w-7xl mx-auto">
          {/* Form Section */}
          <div className="xl:col-span-2">
            <motion.div
              className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 md:p-8 border border-white/10 shadow-2xl"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {/* Form Header */}
              <div className="mb-6 md:mb-8">
                <div className="flex items-center gap-3 mb-2">
                  {React.createElement(steps[currentStep].icon, {
                    className: "h-6 w-6 md:h-7 md:w-7 text-neural-purple"
                  })}
                  <h2 className="text-xl md:text-2xl font-bold text-white">
                    {steps[currentStep].title}
                  </h2>
                </div>
                <p className="text-gray-400 text-sm md:text-base">
                  {steps[currentStep].description}
                </p>
              </div>

              <AnimatePresence mode="wait">
                {currentStep === 0 && <PersonalInfoForm formData={formData} updateFormData={updateFormData} atsAnalysis={atsAnalysis} validationErrors={validationErrors} showValidationErrors={showValidationErrors} />}
                {currentStep === 1 && <EducationForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} atsAnalysis={atsAnalysis} validationErrors={validationErrors} showValidationErrors={showValidationErrors} />}
                {currentStep === 2 && <ExperienceForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} atsAnalysis={atsAnalysis} validationErrors={validationErrors} showValidationErrors={showValidationErrors} />}
                {currentStep === 3 && <SkillsProjectsForm formData={formData} updateFormData={updateFormData} addArrayItem={addArrayItem} removeArrayItem={removeArrayItem} atsAnalysis={atsAnalysis} validationErrors={validationErrors} showValidationErrors={showValidationErrors} />}
                {currentStep === 4 && <ReviewForm formData={formData} updateFormData={updateFormData} atsAnalysis={atsAnalysis} />}
              </AnimatePresence>
            </motion.div>
          </div>

          {/* Preview Section */}
          <div className="xl:col-span-1">
            <motion.div
              className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-4 md:p-6 border border-white/10 sticky top-4 md:top-8 shadow-2xl"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="flex items-center justify-between mb-4 md:mb-6">
                <h3 className="text-base md:text-lg font-semibold flex items-center gap-2">
                  <Eye className="h-4 w-4 md:h-5 md:w-5 text-neural-blue" />
                  Live Preview
                </h3>
                <motion.button
                  onClick={() => setShowPreview(!showPreview)}
                  className="px-3 py-1.5 md:px-4 md:py-2 bg-neural-blue/20 hover:bg-neural-blue/30 border border-neural-blue/50 rounded-lg text-neural-blue hover:text-white transition-all duration-300 text-sm font-medium"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {showPreview ? 'Hide' : 'Show'}
                </motion.button>
              </div>

              {showPreview ? (
                <div className="max-h-[600px] md:max-h-[700px] overflow-y-auto">
                  <EnhancedResumePreview formData={formData} />
                </div>
              ) : (
                <div className="text-center py-12 md:py-16 text-gray-400">
                  <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Eye className="h-16 w-16 md:h-20 md:w-20 mx-auto mb-4 opacity-30" />
                    <p className="text-sm md:text-base mb-2">Preview your resume</p>
                    <p className="text-xs md:text-sm text-gray-500">Click "Show" to see live updates</p>
                  </motion.div>
                </div>
              )}
            </motion.div>

            {/* ATS Optimization Panel */}
            <motion.div
              className="mt-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <ClientOnly>
                <ATSOptimizationPanel
                  formData={formData}
                  atsScore={atsAnalysis.overallScore}
                  suggestions={atsAnalysis.recommendations}
                  realTimeAnalysis={atsAnalysis}
                />
              </ClientOnly>
            </motion.div>
          </div>
        </div>
        {/* Add bottom padding to prevent content from being hidden behind sticky navigation */}
        <div className="pb-24"></div>
        </>
        )}

        {/* Template Selector Modal */}
        {/* Only show template selector on the review step if not generated yet */}
        {currentStep === steps.length - 1 && !resumeGenerated && (
          <TemplateSelector
            selectedTemplate={selectedTemplate}
            onTemplateSelect={setSelectedTemplate}
            onClose={() => setShowTemplateSelector(false)}
          />
        )}

        {/* Sticky Navigation */}
        <StickyNavigation
          currentStep={currentStep}
          totalSteps={steps.length}
          onPrevious={() => {
            if (currentStep > 0) {
              setCurrentStep(currentStep - 1);
            }
          }}
          onNext={nextStep}
          onGenerate={generateResume}
          isGenerating={isGenerating}
          canProceed={currentStep === steps.length - 1 ? canGenerateResume() : canProceedToNextStep(currentStep)}
          steps={steps}
        />
      </div>
    </div>
  );
};

// Skill Input Component (Add placeholder text)
const SkillInput = ({ skills, onAdd, onRemove, placeholder, hasError = false }) => {
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && e.target.value.trim()) {
      onAdd(e.target.value);
      e.target.value = '';
    }
  };

  return (
    <div className="space-y-3">
      <input
        type="text"
        onKeyPress={handleKeyPress}
        className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
          hasError ? 'border-red-500 focus:ring-red-500' : 'border-gray-700'
        }`}
        placeholder={`${placeholder} (Press Enter to add)`}
      />
      <div className="flex flex-wrap gap-2">
        {skills.map((skill, index) => (
          <span
            key={index}
            className="inline-flex items-center gap-1 px-3 py-1 bg-neural-purple/20 border border-neural-purple/50 rounded-full text-sm"
          >
            {skill}
            <button
              onClick={() => onRemove(skill)}
              className="text-neural-pink hover:text-red-400 transition-colors"
            >
              ×
            </button>
          </span>
        ))}
      </div>
    </div>
  );
};

export default ResumeBuilder;