'use client';
import React from 'react';
import WhyQuickFind from '@/oldComponents/WhychoosequickFind';
import { SparklesIcon } from '@heroicons/react/24/solid';
import { motion } from 'framer-motion';

// Custom Icon Components
const FindIcon = () => (
  <svg viewBox="0 0 24 24" className="w-10 h-10 text-neural-purple">
    <path
      fill="currentColor"
      d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 0 0 1.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 0 0-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 0 0 5.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"
    />
    <path
      fill="currentColor"
      d="M19.29 17.29L18 16v-2c-.02-1.33-.53-2.6-1.42-3.54a5.994 5.994 0 0 0-4.08-1.76c-1.55 0-3 .6-4.08 1.76-.89.94-1.4 2.21-1.42 3.54v2l-1.29 1.29c-.63.63-.19 1.71.7 1.71h11.17c.9 0 1.34-1.08.71-1.71z"
      opacity="0.3"
    />
  </svg>
);

const FastIcon = () => (
  <svg viewBox="0 0 24 24" className="w-10 h-10 text-neural-pink">
    <path
      fill="currentColor"
      d="M17.65 6.35A7.958 7.958 0 0 0 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0 1 12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"
    />
  </svg>
);

const SecureIcon = () => (
  <svg viewBox="0 0 24 24" className="w-10 h-10 text-neural-blue">
    <path
      fill="currentColor"
      d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"
    />
  </svg>
);

export default function QuickFind() {
  return (
    <div className="relative overflow-hidden bg-gradient-to-b from-black to-[#0A0A0A] min-h-screen">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      {/* Floating nodes */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-neural-purple opacity-20 blur-xl"
            initial={{
              x: Math.random() * 100 - 50,
              y: Math.random() * 100 - 50,
              width: Math.random() * 200 + 100,
              height: Math.random() * 200 + 100,
            }}
            animate={{
              x: Math.random() * 100 - 50,
              y: Math.random() * 100 - 50,
              transition: {
                duration: Math.random() * 10 + 10,
                repeat: Infinity,
                repeatType: 'reverse',
              },
            }}
          />
        ))}
      </div>

      {/* Header Section */}
      <header className="relative pt-32 pb-20">
        <div className="container mx-auto px-6 text-center">
          <div className="flex justify-center mb-4">
            <SparklesIcon className="h-8 w-8 text-neural-pink" />
          </div>
          <motion.h1 
            className="text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-neural-purple to-neural-pink"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            QuickFind
          </motion.h1>
          <p className="text-xl max-w-2xl mx-auto text-gray-300">
            Lost and Found, Just a Tap Away.
          </p>
        </div>
      </header>

      {/* Features Section */}
      <section className="relative py-16">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center mb-12 text-white">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-neural-purple to-neural-pink">
              Key Features
            </span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { 
                title: 'Lost & Found', 
                description: 'Easily upload and recover lost items with our AI-powered search.', 
                icon: <FindIcon />,
                gradient: 'from-neural-purple to-neural-blue'
              },
              { 
                title: 'Instant Recovery', 
                description: 'Get matched with your lost items in seconds using our neural network.', 
                icon: <FastIcon />,
                gradient: 'from-neural-pink to-neural-purple'
              },
              { 
                title: 'Secure Connections', 
                description: 'End-to-end encrypted communications keep your data private.', 
                icon: <SecureIcon />,
                gradient: 'from-neural-blue to-neural-pink'
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                className="bg-gray-900/50 backdrop-blur-md p-8 rounded-xl border border-white/10 hover:border-neural-purple/30 transition-all group"
                whileHover={{ y: -5 }}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className={`mb-6 p-3 w-16 h-16 rounded-lg bg-gradient-to-r ${feature.gradient} flex items-center justify-center`}>
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                <p className="text-gray-300">{feature.description}</p>
                <div className="mt-4 h-1 w-0 group-hover:w-full bg-gradient-to-r from-neural-purple to-neural-pink transition-all duration-300"></div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <WhyQuickFind />

      {/* Call-to-Action Section */}
      <section className="relative py-20 text-center">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-white mb-6">
            Get Started with QuickFind
          </h2>
          <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
            Join our community and experience the convenience of QuickFind today!
          </p>
          <motion.button 
            className="bg-gradient-to-r from-neural-purple to-neural-pink text-white px-8 py-4 rounded-lg hover:opacity-90 transition"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Download the App
          </motion.button>
        </div>
      </section>
    </div>
  );
}