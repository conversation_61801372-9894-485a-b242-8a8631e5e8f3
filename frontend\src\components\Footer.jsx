import Link from 'next/link'
import { G<PERSON><PERSON>, Twitter, Linkedin } from 'lucide-react'
import { SparklesIcon } from '@heroicons/react/24/solid'

const Footer = () => {
  return (
    <footer className="bg-black border-t border-white/10">
      <div className="mx-auto max-w-7xl px-6 py-12 lg:px-8">
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          <div className="space-y-8">
            <div className="flex items-center gap-2">
              <SparklesIcon className="h-6 w-6 text-neural-purple" />
              <span className="font-mono font-bold text-white">Blink<span className="text-neural-purple">AI</span></span>
            </div>
            <p className="text-sm leading-6 text-gray-400">
              The fastest way to build and deploy AI agents.
            </p>
            <div className="flex space-x-6">
              <Link href="#" className="text-gray-400 hover:text-white">
                <span className="sr-only">Twitter</span>
                <Twitter className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white">
                <span className="sr-only">GitHub</span>
                <Github className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white">
                <span className="sr-only">LinkedIn</span>
                <Linkedin className="h-5 w-5" />
              </Link>
            </div>
          </div>
          <div className="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-white">Product</h3>
                <ul className="mt-6 space-y-4">
                  {['Features', 'Pricing', 'Templates', 'API'].map((item) => (
                    <li key={item}>
                      <Link
                        href="#"
                        className="text-sm leading-6 text-gray-400 hover:text-white"
                      >
                        {item}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-white">Company</h3>
                <ul className="mt-6 space-y-4">
                  {['About', 'Blog', 'Careers', 'Contact'].map((item) => (
                    <li key={item}>
                      <Link
                        href="#"
                        className="text-sm leading-6 text-gray-400 hover:text-white"
                      >
                        {item}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-white">Resources</h3>
                <ul className="mt-6 space-y-4">
                  {['Documentation', 'Guides', 'Community', 'Support'].map((item) => (
                    <li key={item}>
                      <Link
                        href="#"
                        className="text-sm leading-6 text-gray-400 hover:text-white"
                      >
                        {item}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-white">Legal</h3>
                <ul className="mt-6 space-y-4">
                  {['Privacy', 'Terms', 'Security', 'Cookies'].map((item) => (
                    <li key={item}>
                      <Link
                        href="#"
                        className="text-sm leading-6 text-gray-400 hover:text-white"
                      >
                        {item}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-16 border-t border-white/10 pt-8 sm:mt-20 lg:mt-24">
          <p className="text-xs leading-5 text-gray-400">
            &copy; {new Date().getFullYear()} BlinkAI. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}

export default Footer