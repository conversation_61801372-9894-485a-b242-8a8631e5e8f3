"use client";
import React from 'react';
import { Mail, Phone, MapPin, Link, Globe, FileText } from 'lucide-react';

const EnhancedResumePreview = ({ formData }) => {
  const hasContent = formData.personal.firstName || formData.personal.lastName;
  
  if (!hasContent) {
    return (
      <div className="bg-white text-black p-6 rounded-lg text-center">
        <FileText className="h-12 w-12 mx-auto mb-2 text-gray-400" />
        <p className="text-gray-500">Fill out the form to see your resume preview</p>
      </div>
    );
  }

  return (
    <div className="bg-white text-black p-8 rounded-lg text-sm leading-relaxed shadow-lg">
      {/* Header Section */}
      <div className="text-center border-b-2 border-gray-800 pb-6 mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {formData.personal.firstName} {formData.personal.lastName}
        </h1>
        
        {/* Contact Information */}
        <div className="flex flex-wrap justify-center gap-3 text-gray-700 mb-3">
          {formData.personal.email && (
            <span className="flex items-center gap-1">
              <Mail className="h-3 w-3" />
              {formData.personal.email}
            </span>
          )}
          {formData.personal.phone && (
            <span className="flex items-center gap-1">
              <Phone className="h-3 w-3" />
              {formData.personal.phone}
            </span>
          )}
          {formData.personal.location && (
            <span className="flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              {formData.personal.location}
            </span>
          )}
        </div>

        {/* Links */}
        <div className="flex flex-wrap justify-center gap-4 text-blue-600">
          {formData.personal.linkedin && (
            <a href={formData.personal.linkedin} className="flex items-center gap-1 hover:underline">
              <Link className="h-3 w-3" />
              LinkedIn Profile
            </a>
          )}
          {formData.personal.portfolio && (
            <a href={formData.personal.portfolio} className="flex items-center gap-1 hover:underline">
              <Globe className="h-3 w-3" />
              Portfolio Website
            </a>
          )}
        </div>
      </div>

      {/* Professional Summary */}
      {formData.personal.summary && (
        <div className="mb-6">
          <h2 className="text-lg font-bold text-gray-900 border-b border-gray-400 pb-1 mb-3 uppercase tracking-wide">
            Professional Summary
          </h2>
          <p className="text-gray-800 leading-relaxed text-justify">
            {formData.personal.summary}
          </p>
        </div>
      )}

      {/* Experience Section */}
      {formData.experience.length > 0 && formData.experience[0].title && (
        <div className="mb-6">
          <h2 className="text-lg font-bold text-gray-900 border-b border-gray-400 pb-1 mb-3 uppercase tracking-wide">
            Professional Experience
          </h2>
          {formData.experience.map((exp) => (
            exp.title && (
              <div key={exp.id} className="mb-4 last:mb-0">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex-1">
                    <h3 className="text-base font-bold text-gray-900">{exp.title}</h3>
                    <p className="text-gray-700 font-semibold">{exp.company}</p>
                    {exp.location && <p className="text-gray-600 text-sm">{exp.location}</p>}
                  </div>
                  <div className="text-right text-gray-600 text-sm">
                    <p className="font-semibold">
                      {exp.startDate} - {exp.current ? 'Present' : exp.endDate}
                    </p>
                  </div>
                </div>
                {exp.description && (
                  <div className="text-gray-800 leading-relaxed ml-0">
                    {exp.description.split('\n').map((line, i) => (
                      <p key={i} className="mb-1">{line}</p>
                    ))}
                  </div>
                )}
              </div>
            )
          ))}
        </div>
      )}

      {/* Education Section */}
      {formData.education.length > 0 && formData.education[0].degree && (
        <div className="mb-6">
          <h2 className="text-lg font-bold text-gray-900 border-b border-gray-400 pb-1 mb-3 uppercase tracking-wide">
            Education
          </h2>
          {formData.education.map((edu) => (
            edu.degree && (
              <div key={edu.id} className="mb-3 last:mb-0">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="text-base font-bold text-gray-900">{edu.degree}</h3>
                    <p className="text-gray-700 font-semibold">{edu.institution}</p>
                    {edu.location && <p className="text-gray-600 text-sm">{edu.location}</p>}
                    {edu.relevant && (
                      <p className="text-gray-700 text-sm mt-1">
                        <span className="font-semibold">Relevant Coursework:</span> {edu.relevant}
                      </p>
                    )}
                  </div>
                  <div className="text-right text-gray-600 text-sm">
                    <p className="font-semibold">{edu.startDate} - {edu.endDate}</p>
                    {edu.gpa && <p>GPA: {edu.gpa}</p>}
                  </div>
                </div>
              </div>
            )
          ))}
        </div>
      )}

      {/* Skills Section */}
      {(formData.skills.technical.length > 0 || formData.skills.languages.length > 0 || formData.skills.certifications.length > 0) && (
        <div className="mb-6">
          <h2 className="text-lg font-bold text-gray-900 border-b border-gray-400 pb-1 mb-3 uppercase tracking-wide">
            Skills & Certifications
          </h2>
          
          {formData.skills.technical.length > 0 && (
            <div className="mb-3">
              <h3 className="font-bold text-gray-900 mb-1">Technical Skills:</h3>
              <p className="text-gray-800">{formData.skills.technical.join(' • ')}</p>
            </div>
          )}
          
          {formData.skills.languages.length > 0 && (
            <div className="mb-3">
              <h3 className="font-bold text-gray-900 mb-1">Languages:</h3>
              <p className="text-gray-800">{formData.skills.languages.join(' • ')}</p>
            </div>
          )}
          
          {formData.skills.certifications.length > 0 && (
            <div className="mb-3">
              <h3 className="font-bold text-gray-900 mb-1">Certifications:</h3>
              <p className="text-gray-800">{formData.skills.certifications.join(' • ')}</p>
            </div>
          )}
        </div>
      )}

      {/* Projects Section */}
      {formData.projects.length > 0 && formData.projects[0].name && (
        <div className="mb-6">
          <h2 className="text-lg font-bold text-gray-900 border-b border-gray-400 pb-1 mb-3 uppercase tracking-wide">
            Projects
          </h2>
          {formData.projects.map((project) => (
            project.name && (
              <div key={project.id} className="mb-4 last:mb-0">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-base font-bold text-gray-900 flex-1">{project.name}</h3>
                  {project.link && (
                    <a href={project.link} className="text-blue-600 text-sm hover:underline ml-2">
                      View Project
                    </a>
                  )}
                </div>
                {project.description && (
                  <p className="text-gray-800 leading-relaxed mb-2">{project.description}</p>
                )}
                {project.technologies && (
                  <p className="text-gray-700 text-sm">
                    <span className="font-semibold">Technologies Used:</span> {project.technologies}
                  </p>
                )}
              </div>
            )
          ))}
        </div>
      )}
    </div>
  );
};

export default EnhancedResumePreview;
