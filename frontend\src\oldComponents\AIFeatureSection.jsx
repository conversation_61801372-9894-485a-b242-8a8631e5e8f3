'use client'
import { motion } from 'framer-motion'
import { Chip, Terminal, BrainCircuit, Bot } from 'lucide-react'

const features = [
  {
    name: 'AI Agents',
    description: 'Create autonomous agents that can perform tasks, make decisions, and learn from interactions.',
    icon: Bo<PERSON>,
  },
  {
    name: 'No-Code Builder',
    description: 'Visual interface to build complex AI workflows without writing any code.',
    icon: Terminal,
  },
  {
    name: 'Multi-Modal AI',
    description: 'Combine text, image, and voice models for comprehensive AI solutions.',
    icon: BrainCircuit,
  },
  {
    name: 'Enterprise Ready',
    description: 'Scalable infrastructure with SOC 2 compliance and private deployments.',
    icon: Chip,
  },
]

const Features = () => {
  return (
    <div className="py-24 sm:py-32 bg-black">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-base font-semibold leading-7 text-neural-purple"
          >
            AI Platform
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="mt-2 text-3xl font-bold tracking-tight text-white sm:text-4xl"
          >
            Everything you need to build AI
          </motion.p>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            className="mt-6 text-lg leading-8 text-gray-400"
          >
            Our platform provides all the tools and infrastructure to create, deploy, and scale AI solutions in minutes.
          </motion.p>
        </div>
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            {features.map((feature, index) => (
              <motion.div
                key={feature.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex flex-col rounded-xl bg-gray-900/50 p-6 backdrop-blur-sm border border-white/10 hover:border-neural-purple/30 transition-colors"
              >
                <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-neural-purple/10">
                  <feature.icon className="h-6 w-6 text-neural-purple" />
                </div>
                <h3 className="text-base font-semibold leading-7 text-white">{feature.name}</h3>
                <p className="mt-2 text-sm leading-6 text-gray-400">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Features