import React from "react";
import Image from "next/image";

const teamMembers = [
  {
    name: "<PERSON>",
    role: "Founder & CEO",
    bio: "Passionate about transforming the job search process through AI technology to help professionals showcase their best selves.",
    image: "/OurTeamImages/avatar/abdullah.jpg",
    linkedin: "https://www.linkedin.com/in/abdullahkhanspn/",
    github: "https://github.com/Abdullahkhanspn",
  },
  {
    name: "<PERSON><PERSON><PERSON> Fatima",
    role: "Co-Founder",
    bio: "Specializes in developing intelligent algorithms that analyze and optimize resumes for maximum impact with recruiters.",
    image: "/OurTeamImages/avatar/zainab.jpg",
    linkedin: "https://www.linkedin.com/in/zainaboptique?",
    instagram: "https://www.instagram.com/zainaboptique?",
  },
];

const AboutUs = () => {
  return (
    <div className="min-h-screen py-16 pt-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-black to-[#0A0A0A]">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-4xl font-extrabold text-center text-white mb-12">
          <span className="text-gray-300 font-semibold">About</span> AI 
          <span className="text-neural-purple">Resume</span>
        </h1>

        <div className="grid md:grid-cols-2 gap-12 items-center mb-16">
          <div>
            <h2 className="text-2xl font-semibold text-neural-purple mb-4">
              Our Story
            </h2>
            <p className="text-gray-400 mb-4">
              AI Resume Builder was born from the frustration of seeing qualified candidates 
              struggle with outdated resume formats and Applicant Tracking Systems. We combine 
              artificial intelligence with hiring expertise to create resumes that get noticed.
            </p>
            <p className="text-gray-400 mb-4">
              Our platform analyzes thousands of successful resumes and job descriptions to 
              identify what works. We then apply this knowledge to help you craft a personalized, 
              ATS-optimized resume that highlights your unique strengths.
            </p>
            <p className="text-gray-400">
              Whether you're a recent graduate or an experienced professional, our AI-powered 
              tools guide you through the process of creating a resume that stands out in today's 
              competitive job market.
            </p>
          </div>
          <div className="relative h-[300px] md:h-[400px] rounded-xl overflow-hidden border border-white/10">
            <Image
              src="/aboutus.jpeg"
              alt="AI Resume Builder"
              layout="fill"
              objectFit="cover"
              className="opacity-90"
            />
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div className="bg-gray-900/50 backdrop-blur-md p-6 rounded-xl border border-white/10">
            <h2 className="text-2xl font-semibold text-neural-purple mb-4">
              Our Technology
            </h2>
            <p className="text-gray-400">
              We leverage cutting-edge natural language processing and machine learning 
              algorithms to analyze your experience and match it with job requirements. 
              Our system continuously learns from hiring trends to provide the most 
              up-to-date resume optimization strategies.
            </p>
          </div>
          <div className="bg-gray-900/50 backdrop-blur-md p-6 rounded-xl border border-white/10">
            <h2 className="text-2xl font-semibold text-neural-purple mb-4">
              Our Promise
            </h2>
            <p className="text-gray-400">
              We're committed to helping job seekers present their qualifications in the 
              most compelling way possible. Unlike generic templates, our AI provides 
              personalized suggestions tailored to your industry, experience level, and 
              career goals. We never share your data and prioritize your privacy throughout 
              the resume-building process.
            </p>
          </div>
        </div>

        <div className="mb-16">
          <h2 className="text-2xl font-semibold text-neural-purple mb-4 text-center">
            Our Founders
          </h2>
          <p className="text-gray-400 mb-8 text-center max-w-3xl mx-auto">
            The minds behind AI Resume Builder combine expertise in artificial intelligence, 
            recruitment, and user experience to revolutionize how professionals approach 
            job applications.
          </p>
          <div className="flex flex-wrap justify-center gap-8">
            {teamMembers.map((member, index) => (
              <div 
                key={index} 
                className="bg-gray-900/50 backdrop-blur-md rounded-xl p-6 max-w-sm border border-white/10 hover:border-neural-purple/30 transition-all"
              >
                <div className="flex justify-center mb-4">
                  <Image
                    src={member.image}
                    alt={member.name}
                    width={120}
                    height={120}
                    className="rounded-full border-2 border-neural-purple"
                  />
                </div>
                <h3 className="text-xl font-semibold text-center text-white mb-1">
                  {member.name}
                </h3>
                <p className="text-sm text-center text-neural-purple mb-2">
                  {member.role}
                </p>
                <p className="text-sm text-center text-gray-400 mb-4">
                  {member.bio}
                </p>
                <div className="flex justify-center space-x-4">
                  {member.linkedin && (
                    <a
                      href={member.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-neural-purple transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                      </svg>
                    </a>
                  )}
                  {member.github && (
                    <a
                      href={member.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-neural-purple transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                      </svg>
                    </a>
                  )}
                  {member.instagram && (
                    <a
                      href={member.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-neural-purple transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                      </svg>
                    </a>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutUs;