'use client'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { ArrowRight } from 'lucide-react'

const NewsCard = ({ image, title, date, excerpt, href }) => (
  <motion.div 
    whileHover={{ y: -5 }}
    className="glass-effect rounded-xl overflow-hidden border border-white/10 hover:border-primary/30 transition-colors"
  >
    <div className="relative h-48 w-full">
      <Image 
        src={image} 
        alt={title} 
        fill
        className="object-cover"
      />
    </div>
    <div className="p-6">
      <span className="text-sm text-primary">{date}</span>
      <h3 className="text-xl font-bold text-white mt-2 mb-3">{title}</h3>
      <p className="text-gray-400 mb-4">{excerpt}</p>
      <a 
        href={href}
        className="inline-flex items-center text-primary hover:text-primary/80 transition-colors"
      >
        Read more
        <ArrowRight className="w-4 h-4 ml-2" />
      </a>
    </div>
  </motion.div>
)

const NewsAndUpdates = () => {
  const newsItems = [
    {
      image: "/news/ai-revolution.jpg",
      title: "10X Efficiency with AI Agents",
      date: "May 15, 2024",
      excerpt: "How BlinkFind is revolutionizing business processes with AI automation.",
      href: "#"
    },
    {
      image: "/news/app-launch.jpg",
      title: "India's Revolutionary AI App",
      date: "April 28, 2024",
      excerpt: "Our flagship product now available on Play Store.",
      href: "#"
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-b from-[#0A0A0A] to-black">
      <div className="container mx-auto px-6">
        <div className="flex justify-between items-end mb-12">
          <div>
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="mb-4"
            >
              <span className="text-sm font-medium text-primary">NEWS & UPDATES</span>
            </motion.div>
            
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-3xl md:text-4xl font-bold text-white"
            >
              Latest from BlinkFind
            </motion.h2>
          </div>
          
          <motion.a
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            href="#"
            className="text-primary hover:text-primary/80 transition-colors hidden md:flex items-center"
          >
            View all news
            <ArrowRight className="w-4 h-4 ml-2" />
          </motion.a>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {newsItems.map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <NewsCard {...item} />
            </motion.div>
          ))}
        </div>
        
        <div className="mt-8 flex justify-center md:hidden">
          <a href="#" className="text-primary hover:text-primary/80 transition-colors flex items-center">
            View all news
            <ArrowRight className="w-4 h-4 ml-2" />
          </a>
        </div>
      </div>
    </section>
  )
}

export default NewsAndUpdates